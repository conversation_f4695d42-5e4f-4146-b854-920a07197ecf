import OpenAI from 'openai'

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export const SYSTEM_PROMPT = `You are a frontend developer. Return a fully working HTML file styled with Tailwind CSS. It must be responsive, semantic, and well-structured. Do not include explanations.

Requirements:
- Use Tailwind CSS classes for styling
- Make it responsive and mobile-first
- Use semantic HTML elements
- Include proper meta tags and viewport
- Use modern CSS Grid and Flexbox layouts
- Add hover effects and transitions
- Ensure accessibility with proper ARIA labels
- Use a cohesive color scheme
- Include proper typography hierarchy

Return only the complete HTML code without any explanations or markdown formatting.`

export async function generateWebsite(prompt: string): Promise<string> {
  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: SYSTEM_PROMPT,
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      max_tokens: 4000,
      temperature: 0.7,
    })

    return completion.choices[0]?.message?.content || ""
  } catch (error) {
    console.error("OpenAI API error:", error)
    throw new Error("Failed to generate website")
  }
}
