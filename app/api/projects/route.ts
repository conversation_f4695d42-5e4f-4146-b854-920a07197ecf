import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const projects = await db.project.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        updatedAt: "desc",
      },
      select: {
        id: true,
        title: true,
        prompt: true,
        createdAt: true,
        updatedAt: true,
        isPublic: true,
      },
    })

    return NextResponse.json(projects)
  } catch (error) {
    console.error("Projects API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { title, prompt, htmlCode, cssCode, jsCode } = await req.json()

    if (!title || !prompt || !htmlCode) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const project = await db.project.create({
      data: {
        title,
        prompt,
        htmlCode,
        cssCode: cssCode || null,
        jsCode: jsCode || null,
        userId: session.user.id,
      },
    })

    return NextResponse.json(project)
  } catch (error) {
    console.error("Create project API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
