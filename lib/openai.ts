import { PredictionServiceClient } from '@google-cloud/aiplatform'
import { google } from '@google-cloud/aiplatform/build/protos/protos'

// Initialize Vertex AI client
const client = new PredictionServiceClient({
  apiEndpoint: `${process.env.VERTEX_AI_LOCATION || 'us-central1'}-aiplatform.googleapis.com`,
})

const project = process.env.GOOGLE_CLOUD_PROJECT
const location = process.env.VERTEX_AI_LOCATION || 'us-central1'
const publisher = 'google'
const model = 'gemini-1.5-pro-001'

export const SYSTEM_PROMPT = `You are a frontend developer. Return a fully working HTML file styled with Tailwind CSS. It must be responsive, semantic, and well-structured. Do not include explanations.

Requirements:
- Use Tailwind CSS classes for styling
- Make it responsive and mobile-first
- Use semantic HTML elements
- Include proper meta tags and viewport
- Use modern CSS Grid and Flexbox layouts
- Add hover effects and transitions
- Ensure accessibility with proper ARIA labels
- Use a cohesive color scheme
- Include proper typography hierarchy

Return only the complete HTML code without any explanations or markdown formatting.`

export async function generateWebsite(prompt: string): Promise<string> {
  try {
    if (!project) {
      throw new Error("GOOGLE_CLOUD_PROJECT environment variable is not set")
    }

    const endpoint = `projects/${project}/locations/${location}/publishers/${publisher}/models/${model}`

    const instanceValue = {
      messages: [
        {
          role: "user",
          content: `${SYSTEM_PROMPT}\n\nUser request: ${prompt}`
        }
      ]
    }

    const instances = [instanceValue].map(instance =>
      google.protobuf.Value.fromObject(instance)
    )

    const parameters = google.protobuf.Value.fromObject({
      temperature: 0.7,
      maxOutputTokens: 4000,
      topP: 0.8,
      topK: 40
    })

    const request = {
      endpoint,
      instances,
      parameters,
    }

    const [response] = await client.predict(request)

    if (!response.predictions || response.predictions.length === 0) {
      throw new Error("No predictions returned from Vertex AI")
    }

    const prediction = response.predictions[0]
    const content = prediction?.structValue?.fields?.candidates?.listValue?.values?.[0]?.structValue?.fields?.content?.stringValue

    if (!content) {
      throw new Error("Invalid response format from Vertex AI")
    }

    return content
  } catch (error) {
    console.error("Vertex AI API error:", error)
    throw new Error(`Failed to generate website: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
